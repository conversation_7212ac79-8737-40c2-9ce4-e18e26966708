export const APIRoutes = {
  GetPlaygroundAgents: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/playground/agents`,
  // Updated to use new chat stream endpoint
  ChatStream: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/chat/stream`,
  PlaygroundStatus: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/playground/status`,
  // Updated to use new chat sessions endpoint with user_id parameter
  GetChatSessions: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/chat/sessions/`,
  DeletePlaygroundSession: (
    PlaygroundApiUrl: string,
    agentId: string,
    sessionId: string
  ) =>
    `${PlaygroundApiUrl}/v1/playground/agents/${agentId}/sessions/${sessionId}`,

  // Updated to use new chat history endpoint with session_id parameter
  GetChatHistory: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/chat/history/`
}
