# API Migration Summary

## Tóm tắt thay đổi API

Đã cập nhật thành công từ API cũ sang API mới theo yêu cầu:

### 1. API Sessions
**Cũ:** `/v1/playground/agents/{agentId}/sessions`
**Mới:** `/v1/chat/sessions/` với parameter `user_id`

### 2. API Chat History  
**Cũ:** `/v1/chat/history/{sessionId}`
**Mới:** `/v1/chat/history/` với parameter `session_id`

## Các file đã được cập nhật

### 1. `src/services/api/routes.ts`
- Cập nhật `GetPlaygroundSessions` thành `GetChatSessions`
- **LOẠI BỎ HOÀN TOÀN** `GetPlaygroundSession` route
- Cập nhật `GetChatHistory` để không nhận sessionId làm parameter

### 2. `src/types/playground.ts`
- Cập nhật `SessionEntry` interface:
  - `session_id` → `id`
  - `title` → `name`
  - <PERSON><PERSON><PERSON> bỏ `created_at`
- Thêm `ChatSessionsResponse` interface mới

### 3. `src/services/api/playground.ts`
- Cập nhật `getAllPlaygroundSessionsAPI`:
  - Sử dụng URL parameters thay vì path parameters
  - Gọi API mới `/v1/chat/sessions/` với `user_id`
  - Trả về `data.sessions` từ response
- Cập nhật `getChatHistoryAPI`:
  - Sử dụng URL parameters với `session_id`
  - Gọi API mới `/v1/chat/history/`

### 4. `src/hooks/useSessionLoader.tsx`
- **LOẠI BỎ HOÀN TOÀN** `getPlaygroundSessionAPI`
- Cập nhật function `getSession` để sử dụng `getChatHistoryAPI` thay vì API cũ
- Chuyển đổi `ChatHistoryMessage` thành `PlaygroundChatMessage`
- Loại bỏ SessionResponse interface và logic xử lý phức tạp
- Đơn giản hóa logic chỉ sử dụng API chat history mới

### 5. `src/hooks/useAIStreamHandler.tsx`
- Cập nhật tất cả references từ `session_id` thành `id`
- Cập nhật `sessionData` object structure:
  - `session_id` → `id`
  - `title` → `name`
  - Loại bỏ `created_at`

### 6. `src/hooks/useSessionManager.ts`
- Cập nhật filter logic để sử dụng `session.id` thay vì `session.session_id`

### 7. `src/components/Sidebar.tsx`
- Cập nhật tất cả references từ `session.session_id` thành `session.id`
- Cập nhật từ `session.title` thành `session.name`
- Loại bỏ `formatTimestamp` và thay thế bằng text tĩnh
- Thêm `getChatHistory` vào destructuring (để sử dụng trong tương lai)

## Cấu trúc Response mới

### Chat Sessions Response
```json
{
  "sessions": [
    {
      "id": "6549ab40-c459-42f8-94b1-43a716fc3eff",
      "name": "Untitled"
    }
  ],
  "count": 4,
  "user_id": "string"
}
```

### Chat History Response
```json
{
  "history": [
    {
      "role": "system",
      "content": "You are FPT University Agent...",
      "created_at": 1753432008
    }
  ],
  "session_id": "string",
  "count": 3
}
```

## Tính năng mới
- Thêm function `getChatHistory` trong `useSessionLoader` để sử dụng API chat history mới
- Hỗ trợ chuyển đổi từ `ChatHistoryMessage` sang `PlaygroundChatMessage`
- Tự động mapping role `assistant` thành `agent` cho consistency

## Backward Compatibility
- Giữ nguyên các API cũ khác không bị ảnh hưởng
- Các function signature vẫn tương thích với code hiện tại
- Chỉ thay đổi implementation bên trong

## Sửa lỗi bổ sung
### Lỗi "Invalid URL"
- **Nguyên nhân**: `selectedEndpoint` có thể là `/api` (development) không phải URL đầy đủ
- **Giải pháp**: Sử dụng `constructEndpointUrl()` trước khi tạo `new URL()`
- **Files sửa**: `src/services/api/playground.ts`

### Lỗi "sessions showing as undefined"
- **Nguyên nhân**: Debug log vẫn sử dụng `session.session_id` thay vì `session.id`
- **Giải pháp**: Cập nhật debug log trong Sidebar.tsx
- **Files sửa**: `src/components/Sidebar.tsx`

## Testing
- Tất cả các file đã pass diagnostics
- Không có TypeScript errors
- Cấu trúc dữ liệu mới đã được validate
- Sửa lỗi Invalid URL khi gọi API
- Sửa lỗi hiển thị sessions
