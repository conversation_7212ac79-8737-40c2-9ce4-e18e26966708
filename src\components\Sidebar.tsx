import React, { useState } from 'react';
// Optimized individual icon imports for better tree-shaking
import {
  MessageSquare,
  Plus,
  Search,
  GraduationCap,
  Settings,
  HelpCircle,
  X,
  Loader2,
  Trash2
} from 'lucide-react';
// Motion imports for animations
import { motion } from 'motion/react';
import { usePlaygroundStore } from '@/store';
import { SessionEntry } from '@/types/playground';
import useSessionLoader from '@/hooks/useSessionLoader';
import useSessionManager from '@/hooks/useSessionManager';
import { useQueryState } from 'nuqs';
import ConfirmDeleteDialog from './ConfirmDeleteDialog';

interface SidebarProps {
  sessions: SessionEntry[];
  onNewChat: () => void;
  onSelectConversation: (sessionId: string) => void;
  isOpen?: boolean;
  onClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = React.memo(({
  sessions,
  onNewChat,
  onSelectConversation: _onSelectConversation, // Keep for compatibility but not used
  isOpen = false,
  onClose
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [deletingSessionId, setDeletingSessionId] = useState<string | null>(null);
  const [sessionToDelete, setSessionToDelete] = useState<SessionEntry | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { isSessionsLoading } = usePlaygroundStore();
  const { getSession } = useSessionLoader();
  const { deleteSession } = useSessionManager();
  const [agentId] = useQueryState('agent');
  const [currentSessionId, setCurrentSessionId] = useQueryState('session');

  // Debug logs
  console.log('🔍 Current session ID from URL:', currentSessionId);
  console.log('🔍 Available sessions:', sessions.map(s => s.session_id));

  // Filter sessions based on search query
  const filteredSessions = sessions.filter(session =>
    session.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle session click - Load messages directly into chat interface
  const handleSessionClick = async (session: SessionEntry) => {
    if (!agentId) {
      console.error('No agent ID available');
      return;
    }

    try {
      // Update URL parameter first to show immediate visual feedback
      setCurrentSessionId(session.id);

      // Load session messages directly into the chat interface
      await getSession(session.id, agentId);

      // Close sidebar after loading (optional, for better UX)
      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error loading session history:', error);
      // Revert URL parameter if loading failed
      setCurrentSessionId(currentSessionId);
    }
  };

  // Handle delete session - Open dialog
  const handleDeleteSession = (session: SessionEntry, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering session click
    setSessionToDelete(session);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirm delete from dialog
  const handleConfirmDelete = async () => {
    if (!sessionToDelete || !agentId) {
      console.error('No session or agent ID available');
      return;
    }

    try {
      setDeletingSessionId(sessionToDelete.session_id);
      const success = await deleteSession(agentId, sessionToDelete.session_id);

      if (success) {
        // Session will be removed from the list by useSessionManager
        console.log('Session deleted successfully');
        handleCloseDeleteDialog();
      }
    } catch (error) {
      console.error('Error deleting session:', error);
    } finally {
      setDeletingSessionId(null);
    }
  };

  // Handle close delete dialog
  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setSessionToDelete(null);
  };
  return (
    <>
      {/* Mobile overlay removed since sidebar is full-screen */}

      <motion.div
        className={`
          fixed lg:relative top-0 left-0 z-50 lg:z-auto
          w-full lg:w-64 max-w-none lg:max-w-none
          bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900
          text-white flex flex-col h-screen lg:h-full overflow-hidden
          shadow-2xl lg:shadow-none
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}
        style={{ pointerEvents: 'auto' }}
      >
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-purple-600/10"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent"></div>

      {/* Header */}
      <motion.div
        className="relative p-3 sm:p-4 border-b border-gray-700/50 backdrop-blur-sm"
        initial={{ y: -50, opacity: 0 }}
        animate={{
          y: 0,
          opacity: 1,
          transition: { delay: 0.2, duration: 0.4 }
        }}
      >
        <motion.div
          className="flex items-center justify-between mb-3 sm:mb-4"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{
            scale: 1,
            opacity: 1,
            transition: { delay: 0.3, duration: 0.4 }
          }}
          style={{ pointerEvents: 'auto' }}
        >
          <div className="flex items-center gap-3">
            <motion.div
              className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg ring-2 ring-blue-400/30"
              whileHover={{
                scale: 1.1,
                rotate: 10,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
            >
              <GraduationCap className="w-5 h-5" />
            </motion.div>
            <div className="min-w-0 flex-1">
              <h1 className="font-bold text-xs sm:text-sm bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent truncate">FPT University</h1>
              <p className="text-xs text-blue-300/80 truncate">Tư vấn tuyển sinh AI</p>
            </div>
          </div>
        </motion.div>

        {/* Close button for mobile - Outside header container */}
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (onClose) {
              onClose();
            }
          }}
          className="lg:hidden absolute mb-2 top-4 right-4 p-2.5 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded-lg transition-all duration-200 touch-manipulation flex-shrink-0 z-50"
          aria-label="Đóng sidebar"
          type="button"
          style={{
            pointerEvents: 'auto',
            touchAction: 'manipulation'
          }}
        >
          <X className="w-5 h-5" />
        </button>

        <motion.button
          onClick={onNewChat}
          className="w-full flex items-center gap-2 sm:gap-3 px-3 py-2.5 sm:py-2 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg"
          initial={{ y: 20, opacity: 0 }}
          animate={{
            y: 0,
            opacity: 1,
            transition: { delay: 0.4, duration: 0.3 }
          }}
          whileHover={{
            scale: 1.02,
            boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.25)",
            transition: { duration: 0.2 }
          }}
          whileTap={{ scale: 0.98 }}
        >
          <Plus className="w-4 h-4 flex-shrink-0" />
          <span className="text-xs sm:text-sm font-medium truncate">Cuộc trò chuyện mới</span>
        </motion.button>
      </motion.div>

      {/* Search */}
      <motion.div
        className="relative p-3 sm:p-4"
        initial={{ y: 20, opacity: 0 }}
        animate={{
          y: 0,
          opacity: 1,
          transition: { delay: 0.5, duration: 0.3 }
        }}
      >
        <div className="relative">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <motion.input
            type="text"
            placeholder="Tìm kiếm..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-3 py-2.5 sm:py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400/50"
            whileFocus={{
              scale: 1.02,
              boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
              transition: { duration: 0.2 }
            }}
          />
        </div>
      </motion.div>

      {/* Conversations */}
      <motion.div
        className="relative flex-1 overflow-y-auto px-2 sm:px-3 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { delay: 0.6, duration: 0.4 }
        }}
      >
        <div className="space-y-1">
{isSessionsLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-5 h-5 animate-spin text-gray-400" />
              <span className="ml-2 text-sm text-gray-400">Đang tải...</span>
            </div>
          ) : filteredSessions.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="w-8 h-8 text-gray-500 mx-auto mb-2" />
              <p className="text-sm text-gray-400">
                {searchQuery ? 'Không tìm thấy cuộc trò chuyện' : 'Chưa có cuộc trò chuyện nào'}
              </p>
            </div>
          ) : (
            filteredSessions.map((session, index) => {
              const isCurrentSession = currentSessionId === session.id;

              // Debug log for each session
              console.log(`🎯 Session ${session.id}: isCurrentSession = ${isCurrentSession}`);

              return (
                <motion.div
                  key={session.id}
                  className={`relative group ${isCurrentSession ? 'z-10' : ''}`}
                  initial={{ x: -50, opacity: 0 }}
                  animate={{
                    x: 0,
                    opacity: 1,
                    transition: { delay: 0.7 + index * 0.1, duration: 0.3 }
                  }}
                >
                <div className="flex items-stretch gap-1 w-full pl-2">
                  {/* Main Session Button */}
                  <motion.button
                    onClick={() => handleSessionClick(session)}
                    className={`
                      flex-1 text-left px-3 py-2.5 rounded-lg touch-manipulation transition-all duration-300 min-w-0 relative overflow-hidden
                      ${isCurrentSession
                        ? 'bg-gradient-to-r from-blue-600/20 to-purple-600/20 text-white border border-blue-500/30 shadow-lg shadow-blue-500/20'
                        : 'text-gray-300 hover:bg-gray-800/50'
                      }
                    `}
                    whileHover={{
                      scale: isCurrentSession ? 1.02 : 1.01,
                      backgroundColor: isCurrentSession
                        ? "rgba(59, 130, 246, 0.25)"
                        : "rgba(55, 65, 81, 0.5)",
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.99 }}
                  >
                    {/* Active session background indicator */}
                    {isCurrentSession && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 rounded-lg"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}

                    {/* Active session left indicator - inside button */}
                    {isCurrentSession && (
                      <motion.div
                        className="absolute left-1 top-3 translate-y-1/2 w-1 h-7 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full shadow-md shadow-blue-500/30"
                        initial={{ opacity: 0, scaleY: 0 }}
                        animate={{ opacity: 1, scaleY: 1 }}
                        transition={{ duration: 0.4, ease: "easeOut" }}
                      />
                    )}

                    <div className="flex items-center gap-3 relative z-10 pl-4">
                      <motion.div
                        className="flex-shrink-0"
                        whileHover={{ rotate: 10 }}
                        animate={isCurrentSession ? {
                          scale: [1, 1.1, 1],
                          rotate: [0, 5, 0]
                        } : {}}
                        transition={isCurrentSession ? {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        } : {
                          duration: 0.2
                        }}
                      >
                        <MessageSquare className={`w-4 h-4 ${
                          isCurrentSession
                            ? 'text-blue-400 drop-shadow-sm'
                            : 'text-gray-400'
                        }`} />
                      </motion.div>
                      <div className="flex-1 min-w-0">
                        <p className={`text-sm font-medium truncate leading-tight ${
                          isCurrentSession
                            ? 'text-white font-semibold'
                            : 'text-gray-200'
                        }`}>
                          {session.name}
                        </p>
                        <p className={`text-xs truncate mt-0.5 ${
                          isCurrentSession
                            ? 'text-blue-200'
                            : 'text-gray-400'
                        }`}>
                          Cuộc trò chuyện
                        </p>
                      </div>
                    </div>
                  </motion.button>

                  {/* Delete Session Button */}
                  <motion.button
                    onClick={(e) => handleDeleteSession(session, e)}
                    disabled={deletingSessionId === session.id}
                    className="flex-shrink-0 self-center p-2 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded-md opacity-0 group-hover:opacity-100 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    title="Xóa cuộc trò chuyện"
                  >
                    {deletingSessionId === session.id ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4" />
                    )}
                  </motion.button>
                </div>
              </motion.div>
              );
            })
          )}
        </div>
      </motion.div>

      {/* Footer */}
      <div className="relative p-2 sm:p-3 lg:p-4 border-t border-gray-700/50">
        <div className="flex items-center justify-center gap-2 sm:gap-4 sm:justify-between">
          <button className="p-2 sm:p-2 text-gray-400 hover:text-blue-300 hover:bg-gray-800/50 rounded-lg transition-all duration-200 hover:scale-105 touch-manipulation">
            <Settings className="w-4 h-4" />
          </button>
          <button className="p-2 sm:p-2 text-gray-400 hover:text-blue-300 hover:bg-gray-800/50 rounded-lg transition-all duration-200 hover:scale-105 touch-manipulation">
            <HelpCircle className="w-4 h-4" />
          </button>
        </div>
      </div>
    </motion.div>

    {/* Confirm Delete Dialog */}
    <ConfirmDeleteDialog
      isOpen={isDeleteDialogOpen}
      onClose={handleCloseDeleteDialog}
      onConfirm={handleConfirmDelete}
      sessionTitle={sessionToDelete?.title || ''}
      isDeleting={deletingSessionId === sessionToDelete?.session_id}
    />

    </>
  );
});

export default Sidebar;